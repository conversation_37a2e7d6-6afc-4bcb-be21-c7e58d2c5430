using MelonLoader;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// Simple UI for managing Lua scripts - KISS principle: just select and start/stop scripts.
/// </summary>
public class LuaScriptUI
{
    private string? _selectedScript;
    private bool _isRunning;
    private List<string> _availableScripts = new();
    private int _selectedScriptIndex = -1;
    private float _lastRefresh;
    private const float REFRESH_INTERVAL = 2f; // Refresh script list every 2 seconds

    /// <summary>
    /// Draw the Lua script UI. Call this from your main OnGUI method.
    /// </summary>
    public void DrawUI()
    {
        if (!LuaManager.Instance.IsInitialized)
        {
            GUILayout.Label("Lua Manager not initialized", GUILayout.Height(20));
            return;
        }

        // Refresh script list periodically
        if (Time.time - _lastRefresh > REFRESH_INTERVAL)
        {
            RefreshScriptList();
            _lastRefresh = Time.time;
        }

        GUILayout.BeginVertical("box");
        GUILayout.Label("Lua Script Control", GUILayout.Height(20));

        // Script selection dropdown
        DrawScriptSelection();

        // Start/Stop controls
        DrawScriptControls();

        // Status display
        DrawStatus();

        GUILayout.EndVertical();
    }

    private void DrawScriptSelection()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("Script:", GUILayout.Width(50));

        if (_availableScripts.Count == 0)
        {
            GUILayout.Label("No scripts found", GUILayout.Height(20));
        }
        else
        {
            // Simple dropdown using buttons
            var currentScriptName = _selectedScriptIndex >= 0 && _selectedScriptIndex < _availableScripts.Count 
                ? _availableScripts[_selectedScriptIndex] 
                : "Select Script";

            if (GUILayout.Button(currentScriptName, GUILayout.Width(150), GUILayout.Height(20)))
            {
                // Cycle through scripts
                _selectedScriptIndex = (_selectedScriptIndex + 1) % _availableScripts.Count;
                _selectedScript = _availableScripts[_selectedScriptIndex];
            }

            // Previous script button
            if (GUILayout.Button("<", GUILayout.Width(20), GUILayout.Height(20)) && _availableScripts.Count > 0)
            {
                _selectedScriptIndex = (_selectedScriptIndex - 1 + _availableScripts.Count) % _availableScripts.Count;
                _selectedScript = _availableScripts[_selectedScriptIndex];
            }

            // Next script button  
            if (GUILayout.Button(">", GUILayout.Width(20), GUILayout.Height(20)) && _availableScripts.Count > 0)
            {
                _selectedScriptIndex = (_selectedScriptIndex + 1) % _availableScripts.Count;
                _selectedScript = _availableScripts[_selectedScriptIndex];
            }
        }

        GUILayout.EndHorizontal();
    }

    private void DrawScriptControls()
    {
        GUILayout.BeginHorizontal();

        // Start button
        GUI.enabled = !_isRunning && !string.IsNullOrEmpty(_selectedScript);
        if (GUILayout.Button("Start", GUILayout.Width(60), GUILayout.Height(25)))
        {
            StartScript();
        }

        // Stop button
        GUI.enabled = _isRunning;
        if (GUILayout.Button("Stop", GUILayout.Width(60), GUILayout.Height(25)))
        {
            StopScript();
        }

        GUI.enabled = true;

        // Refresh button
        if (GUILayout.Button("Refresh", GUILayout.Width(60), GUILayout.Height(25)))
        {
            RefreshScriptList();
        }

        GUILayout.EndHorizontal();
    }

    private void DrawStatus()
    {
        var statusText = _isRunning 
            ? $"Running: {_selectedScript}" 
            : "Stopped";
        
        var statusColor = _isRunning ? Color.green : Color.gray;
        
        var oldColor = GUI.color;
        GUI.color = statusColor;
        GUILayout.Label($"Status: {statusText}", GUILayout.Height(20));
        GUI.color = oldColor;

        // Show script count
        GUILayout.Label($"Available scripts: {_availableScripts.Count}", GUILayout.Height(15));
    }

    private void StartScript()
    {
        if (string.IsNullOrEmpty(_selectedScript))
        {
            MelonLogger.Warning("No script selected");
            return;
        }

        try
        {
            // Load the script if not already loaded
            if (!LuaManager.Instance.Scripts.ContainsKey(_selectedScript))
            {
                if (!LuaManager.Instance.LoadScript(_selectedScript))
                {
                    MelonLogger.Error($"Failed to load script: {_selectedScript}");
                    return;
                }
            }

            _isRunning = true;
            MelonLogger.Msg($"Started Lua script: {_selectedScript}");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error starting script {_selectedScript}: {ex.Message}");
            _isRunning = false;
        }
    }

    private void StopScript()
    {
        _isRunning = false;
        MelonLogger.Msg($"Stopped Lua script: {_selectedScript}");
    }

    private void RefreshScriptList()
    {
        _availableScripts = LuaManager.Instance.GetAvailableScripts();
        
        // Validate current selection
        if (!string.IsNullOrEmpty(_selectedScript) && !_availableScripts.Contains(_selectedScript))
        {
            _selectedScript = null;
            _selectedScriptIndex = -1;
            _isRunning = false;
        }
        else if (!string.IsNullOrEmpty(_selectedScript))
        {
            _selectedScriptIndex = _availableScripts.IndexOf(_selectedScript);
        }
    }

    /// <summary>
    /// Call the OnUpdate function of the currently running script.
    /// Call this from your main OnUpdate method.
    /// </summary>
    public void UpdateRunningScript()
    {
        if (!_isRunning || string.IsNullOrEmpty(_selectedScript))
            return;

        try
        {
            LuaManager.Instance.CallFunction(_selectedScript, "OnUpdate");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error updating script {_selectedScript}: {ex.Message}");
            _isRunning = false;
        }
    }

    /// <summary>
    /// Check if a script is currently running.
    /// </summary>
    public bool IsScriptRunning => _isRunning;

    /// <summary>
    /// Get the name of the currently selected script.
    /// </summary>
    public string? CurrentScript => _selectedScript;
}
