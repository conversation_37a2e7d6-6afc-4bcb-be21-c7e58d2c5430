-- UI Test Script
-- Simple script to test the Lua UI functionality

local lastTime = 0
local counter = 0

function OnUpdate()
    local currentTime = Time()

    -- Only run every 2 seconds
    if currentTime - lastTime < 2.0 then
        return
    end

    lastTime = currentTime
    counter = counter + 1

    -- Simple test output
    Log("UI Test Script running - Count: " .. counter)

    -- Get player info if available
    local player = GetPlayer()
    if player then
        local pos = GetPlayerPosition()
        if pos then
            Log("Player position: " .. string.format("%.1f, %.1f, %.1f", pos.x, pos.y, pos.z))
        end

        local health = GetPlayerHealth()
        if health then
            Log("Player health: " .. string.format("%.0f", health))
        end

        Log("Player alive: " .. tostring(IsPlayerAlive()))
    else
        Log("No player found")
    end

    -- Test some input
    if GetKeyDown("f1") then
        Log("F1 key detected!")
    end

    Log("Script cycle " .. counter .. " completed")
end
