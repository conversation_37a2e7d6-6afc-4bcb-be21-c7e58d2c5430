using MelonLoader;
using MoonSharp.Interpreter;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// Simple API bridge for Lua scripts.
/// </summary>
public static class LuaAPI
{
    public static void RegisterAPI(Script script)
    {
        // Game objects
        script.Globals["GetPlayer"] = (Func<object?>)(() => PLAYER);
        script.Globals["GetEnemies"] = (Func<object>)(() => ENEMIES);
        script.Globals["GetGroundItems"] = (Func<object>)(() => GROUND_ITEMS);
        script.Globals["GetInteractables"] = (Func<object>)(() => INTERACTABLES);

        // Player functions
        script.Globals["GetPlayerPosition"] = (Func<Vector3?>)PlayerHelpers.GetPlayerPosition;
        script.Globals["GetPlayerHealth"] = (Func<float?>)PlayerHelpers.GetPlayerHealth;
        script.Globals["IsPlayerAlive"] = (Func<bool>)PlayerHelpers.IsPlayerAlive;

        // Utility functions
        script.Globals["Log"] = (Action<string>)(msg => MelonLogger.Msg($"[Script] {msg}"));
        script.Globals["Distance"] = (Func<Vector3, Vector3, float>)Vector3.Distance;
        script.Globals["Vector3"] = (Func<float, float, float, Vector3>)((x, y, z) => new Vector3(x, y, z));
        script.Globals["GetKey"] = (Func<string, bool>)Input.GetKey;
        script.Globals["GetKeyDown"] = (Func<string, bool>)Input.GetKeyDown;
        script.Globals["Time"] = (Func<float>)(() => Time.time);
        script.Globals["Random"] = (Func<float>)(() => UnityEngine.Random.value);
    }
}
