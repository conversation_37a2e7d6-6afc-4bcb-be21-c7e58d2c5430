using Il2Cpp;
using <PERSON>onLoader;
using MoonSharp.Interpreter;
using TestLE.Types;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// Simple API bridge for Lua scripts.
/// </summary>
public static class LuaAPI
{
    private static bool _typesRegistered = false;

    /// <summary>
    /// Register all necessary types with MoonSharp for proper interop.
    /// This should be called once before creating any scripts.
    /// </summary>
    public static void RegisterTypes()
    {
        if (_typesRegistered) return;

        try
        {
            // Unity basic types
            UserData.RegisterType<Vector3>();
            UserData.RegisterType<Transform>();
            UserData.RegisterType<GameObject>();
            UserData.RegisterType<Camera>();

            // Game-specific types that might be exposed to Lua
            UserData.RegisterType<LocalPlayer>();
            UserData.RegisterType<Enemy>();
            UserData.RegisterType<GroundItem>();
            UserData.RegisterType<WorldObjectClickListener>();

            // Collections (if needed)
            UserData.RegisterType<List<Enemy>>();
            UserData.RegisterType<List<GroundItem>>();
            UserData.RegisterType<List<WorldObjectClickListener>>();

            _typesRegistered = true;
            MelonLogger.Msg("MoonSharp types registered successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to register MoonSharp types: {ex.Message}");
        }
    }

    public static void RegisterAPI(Script script)
    {
        // Ensure types are registered first
        RegisterTypes();
        // Game objects - return proper UserData instances
        script.Globals["GetPlayer"] = (Func<DynValue>)(() =>
            PLAYER != null ? UserData.Create(PLAYER) : DynValue.Nil);
        script.Globals["GetEnemies"] = (Func<DynValue>)(() =>
            UserData.Create(ENEMIES));
        script.Globals["GetGroundItems"] = (Func<DynValue>)(() =>
            UserData.Create(GROUND_ITEMS));
        script.Globals["GetInteractables"] = (Func<DynValue>)(() =>
            UserData.Create(INTERACTABLES));

        // Player functions - return proper types
        script.Globals["GetPlayerPosition"] = (Func<DynValue>)(() => {
            var pos = PlayerHelpers.GetPlayerPosition();
            return pos.HasValue ? UserData.Create(pos.Value) : DynValue.Nil;
        });
        script.Globals["GetPlayerHealth"] = (Func<DynValue>)(() => {
            var health = PlayerHelpers.GetPlayerHealth();
            return health.HasValue ? DynValue.NewNumber(health.Value) : DynValue.Nil;
        });
        script.Globals["IsPlayerAlive"] = (Func<bool>)PlayerHelpers.IsPlayerAlive;
        script.Globals["IsPlayerInCombat"] = (Func<bool>)(() => {
            // Add combat check if available
            return false; // Placeholder
        });

        // Utility functions
        script.Globals["Log"] = (Action<string>)(msg => MelonLogger.Msg($"[Script] {msg}"));
        script.Globals["Distance"] = (Func<DynValue, DynValue, double>)((v1, v2) => {
            try {
                var vec1 = v1.UserData.Object as Vector3? ?? Vector3.zero;
                var vec2 = v2.UserData.Object as Vector3? ?? Vector3.zero;
                return Vector3.Distance(vec1, vec2);
            } catch {
                return 0.0;
            }
        });
        script.Globals["Vector3"] = (Func<double, double, double, DynValue>)((x, y, z) =>
            UserData.Create(new Vector3((float)x, (float)y, (float)z)));
        script.Globals["GetKey"] = (Func<string, bool>)Input.GetKey;
        script.Globals["GetKeyDown"] = (Func<string, bool>)Input.GetKeyDown;
        script.Globals["Time"] = (Func<double>)(() => Time.time);
        script.Globals["Random"] = (Func<double>)(() => UnityEngine.Random.value);

        // Additional utility functions
        script.Globals["DeltaTime"] = (Func<double>)(() => Time.deltaTime);
        script.Globals["FrameCount"] = (Func<int>)(() => Time.frameCount);

        // Math utilities
        script.Globals["Sqrt"] = (Func<double, double>)Math.Sqrt;
        script.Globals["Sin"] = (Func<double, double>)Math.Sin;
        script.Globals["Cos"] = (Func<double, double>)Math.Cos;
        script.Globals["Abs"] = (Func<double, double>)Math.Abs;
        script.Globals["Min"] = (Func<double, double, double>)Math.Min;
        script.Globals["Max"] = (Func<double, double, double>)Math.Max;

        // Type checking utilities
        script.Globals["IsNil"] = (Func<DynValue, bool>)(val => val.IsNil());
        script.Globals["Type"] = (Func<DynValue, string>)(val => val.Type.ToString());
    }
}
